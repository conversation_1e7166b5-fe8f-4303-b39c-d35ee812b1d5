#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL连接测试脚本
用于测试和诊断SSL证书验证问题
"""

import requests
import ssl
import urllib3
import sys
from urllib.parse import urlparse

def test_ssl_connection(url):
    """测试SSL连接的各种方法"""
    print(f"测试URL: {url}")
    print("=" * 60)
    
    # 解析URL
    parsed_url = urlparse(url)
    host = parsed_url.hostname
    port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
    
    print(f"主机: {host}")
    print(f"端口: {port}")
    print("-" * 60)
    
    # 测试1: 标准requests请求（验证SSL）
    print("测试1: 标准requests请求（验证SSL）")
    try:
        response = requests.get(url, timeout=10, verify=False)
        print(f"✓ 成功 - 状态码: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
    except Exception as e:
        print(f"✗ 失败 - {type(e).__name__}: {e}")
    print()
    
    # 测试2: 忽略SSL验证
    print("测试2: 忽略SSL验证 (verify=False)")
    try:
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        response = requests.get(url, verify=False, timeout=10)
        print(f"✓ 成功 - 状态码: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
    except Exception as e:
        print(f"✗ 失败 - {type(e).__name__}: {e}")
    print()
    
    # 测试3: 使用Session忽略SSL
    print("测试3: 使用Session忽略SSL")
    try:
        session = requests.Session()
        session.verify = False
        response = session.get(url, timeout=10)
        print(f"✓ 成功 - 状态码: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
    except Exception as e:
        print(f"✗ 失败 - {type(e).__name__}: {e}")
    print()
    
    # 测试4: 自定义SSL上下文
    print("测试4: 自定义SSL上下文")
    try:
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # 使用自定义适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.ssl_ import create_urllib3_context
        
        class SSLAdapter(HTTPAdapter):
            def init_poolmanager(self, *args, **kwargs):
                context = create_urllib3_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                kwargs['ssl_context'] = context
                return super().init_poolmanager(*args, **kwargs)
        
        session = requests.Session()
        session.mount('https://', SSLAdapter())
        response = session.get(url, timeout=10)
        print(f"✓ 成功 - 状态码: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
    except Exception as e:
        print(f"✗ 失败 - {type(e).__name__}: {e}")
    print()
    
    # 测试5: 原始socket连接
    print("测试5: 原始socket连接")
    try:
        import socket
        
        # 创建socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        if parsed_url.scheme == 'https':
            # 包装SSL
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            ssl_sock = context.wrap_socket(sock, server_hostname=host)
            ssl_sock.connect((host, port))
            print(f"✓ SSL连接成功")
            
            # 发送HTTP请求
            request = f"GET {parsed_url.path or '/'} HTTP/1.1\r\nHost: {host}\r\nConnection: close\r\n\r\n"
            ssl_sock.send(request.encode())
            
            # 读取响应头
            response_data = ssl_sock.recv(1024).decode('utf-8', errors='ignore')
            status_line = response_data.split('\r\n')[0]
            print(f"  响应: {status_line}")
            
            ssl_sock.close()
        else:
            sock.connect((host, port))
            print(f"✓ TCP连接成功")
            sock.close()
            
    except Exception as e:
        print(f"✗ 失败 - {type(e).__name__}: {e}")
    print()
    
    # 测试6: 检查SSL证书信息
    print("测试6: 检查SSL证书信息")
    try:
        import socket
        import ssl
        
        context = ssl.create_default_context()
        
        with socket.create_connection((host, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=host) as ssock:
                cert = ssock.getpeercert()
                print(f"✓ 证书获取成功")
                print(f"  主题: {cert.get('subject', 'N/A')}")
                print(f"  颁发者: {cert.get('issuer', 'N/A')}")
                print(f"  有效期: {cert.get('notBefore', 'N/A')} - {cert.get('notAfter', 'N/A')}")
                
    except Exception as e:
        print(f"✗ 失败 - {type(e).__name__}: {e}")
    print()

def main():
    """主函数"""
    # 测试URL
    test_url = "https://pdf1.webgetstore.com/2025/05/20/2cfa21319e852b9af6a0bbb31867282e.txt?sg=91342312df04100831a2096404a0b3be&e=68371f95&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt"
    
    print("SSL连接诊断工具")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Requests版本: {requests.__version__}")
    print(f"urllib3版本: {urllib3.__version__}")
    print(f"SSL版本: {ssl.OPENSSL_VERSION}")
    print("=" * 60)
    print()
    
    # 如果提供了命令行参数，使用它作为测试URL
    if len(sys.argv) > 1:
        test_url = sys.argv[1]
    
    test_ssl_connection(test_url)
    
    print("测试完成！")
    print("\n建议:")
    print("1. 如果测试2成功，说明SSL证书验证是问题所在")
    print("2. 如果所有测试都失败，可能是网络连接问题")
    print("3. 如果只有测试1失败，可以在代码中使用verify=False")

if __name__ == "__main__":
    main()
